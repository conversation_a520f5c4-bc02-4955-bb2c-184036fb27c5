const { remote } = require('webdriverio');

async function runSimpleTest() {
    console.log('🚀 Starting simple APK test...');
    
    const opts = {
        protocol: 'http',
        hostname: 'localhost',
        port: 4723,
        path: '/',
        capabilities: {
            platformName: 'Android',
            'appium:deviceName': 'Android Emulator',
            'appium:platformVersion': '12',
            'appium:app': require('path').join(process.cwd(), 'app-dev-release.apk'),
            'appium:automationName': 'UiAutomator2',
            'appium:newCommandTimeout': 240,
            'appium:noReset': false,
            'appium:fullReset': false
        }
    };

    let driver;
    
    try {
        console.log('📱 Connecting to Appium server...');
        driver = await remote(opts);
        
        console.log('✅ Connected to Appium successfully!');
        
        // Wait for app to launch
        console.log('⏳ Waiting for app to launch...');
        await driver.pause(5000);
        
        // Get basic app information
        console.log('📋 Getting app information...');
        const currentActivity = await driver.getCurrentActivity();
        const currentPackage = await driver.getCurrentPackage();
        
        console.log(`📱 Current Activity: ${currentActivity}`);
        console.log(`📦 Current Package: ${currentPackage}`);
        
        // Take a screenshot
        console.log('📸 Taking screenshot...');
        const screenshot = await driver.takeScreenshot();
        require('fs').writeFileSync('app-screenshot.png', screenshot, 'base64');
        console.log('✅ Screenshot saved as app-screenshot.png');
        
        // Get page source
        console.log('🔍 Getting page source...');
        const pageSource = await driver.getPageSource();
        require('fs').writeFileSync('app-page-source.xml', pageSource);
        console.log('✅ Page source saved as app-page-source.xml');
        
        // Try to find some elements
        console.log('🔎 Looking for UI elements...');
        const elements = await driver.$$('*');
        console.log(`📊 Found ${elements.length} UI elements`);
        
        // Test basic interactions
        console.log('👆 Testing basic interactions...');
        
        // Try swipe down
        const { width, height } = await driver.getWindowSize();
        await driver.touchAction([
            { action: 'press', x: width / 2, y: height * 0.3 },
            { action: 'moveTo', x: width / 2, y: height * 0.7 },
            { action: 'release' }
        ]);
        
        await driver.pause(2000);
        
        // Try swipe up
        await driver.touchAction([
            { action: 'press', x: width / 2, y: height * 0.7 },
            { action: 'moveTo', x: width / 2, y: height * 0.3 },
            { action: 'release' }
        ]);
        
        console.log('✅ Basic interactions completed');
        
        // Test back button
        console.log('⬅️ Testing back button...');
        await driver.back();
        await driver.pause(1000);
        
        console.log('🎉 Test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('💡 Make sure Appium server is running: npx appium --port 4723');
        } else if (error.message.includes('device')) {
            console.log('💡 Make sure an Android device or emulator is connected');
            console.log('💡 Check with: adb devices');
        } else if (error.message.includes('app')) {
            console.log('💡 Make sure the APK file exists: app-dev-release.apk');
        }
        
        throw error;
    } finally {
        if (driver) {
            console.log('🔌 Closing connection...');
            await driver.deleteSession();
        }
    }
}

// Run the test
runSimpleTest().catch(console.error);
